{"time":"2025-08-16T15:15:57.40548466+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-16T15:15:57.543974464+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-16T15:15:57.54437879+08:00","level":"INFO","msg":"stream: created new stream","id":"drflgpg4"}
{"time":"2025-08-16T15:15:57.544428585+08:00","level":"INFO","msg":"stream: started","id":"drflgpg4"}
{"time":"2025-08-16T15:15:57.544512478+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"drflgpg4"}
{"time":"2025-08-16T15:15:57.544596766+08:00","level":"INFO","msg":"sender: started","stream_id":"drflgpg4"}
{"time":"2025-08-16T15:15:57.54465155+08:00","level":"INFO","msg":"handler: started","stream_id":"drflgpg4"}
{"time":"2025-08-16T15:15:57.545610087+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
