------------- 16-17-42-43-835736 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
------------- 16-17-42-43-835929 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
------------- 16-17-42-43-836171 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
------------- 16-17-42-43-837254 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
------------- 16-17-45-30-986104 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([False], device='cuda:1'), 'visual_anomaly': tensor([True], device='cuda:1'), 'audio_anomaly': tensor([True], device='cuda:1'), 'visual_boost': tensor([0.1602], device='cuda:1', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'audio_boost': tensor([0.1680], device='cuda:1', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:1', dtype=torch.int32), 'confidences': {'full': tensor([0.8320], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9922], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([1.], device='cuda:1', dtype=torch.bfloat16, grad_fn=<StackBackward0>)}}
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=0.000
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.000
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.000
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=0.000
------------- 16-17-45-30-987009 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([False], device='cuda:3'), 'visual_anomaly': tensor([False], device='cuda:3'), 'audio_anomaly': tensor([True], device='cuda:3'), 'visual_boost': tensor([-0.1641], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'audio_boost': tensor([0.1406], device='cuda:3', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:3', dtype=torch.int32), 'confidences': {'full': tensor([0.7930], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.6289], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.9336], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 752.25
Completion 0: length=582, L/Lavg=0.774, ra=0.0, reward=0.000
Completion 1: length=1264, L/Lavg=1.680, ra=0.0, reward=0.000
Completion 2: length=743, L/Lavg=0.988, ra=0.0, reward=0.000
Completion 3: length=420, L/Lavg=0.558, ra=0.0, reward=0.000
------------- 16-17-45-30-986982 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([False], device='cuda:2'), 'visual_anomaly': tensor([False], device='cuda:2'), 'audio_anomaly': tensor([False], device='cuda:2'), 'visual_boost': tensor([0.0195], device='cuda:2', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'audio_boost': tensor([0.0547], device='cuda:2', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([0], device='cuda:2', dtype=torch.int32), 'confidences': {'full': tensor([0.9453], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9648], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([1.], device='cuda:2', dtype=torch.bfloat16, grad_fn=<StackBackward0>)}}
Average Length (Lavg): 811.25
Completion 0: length=1146, L/Lavg=1.413, ra=1.0, reward=0.000
Completion 1: length=796, L/Lavg=0.981, ra=0.0, reward=-0.019
Completion 2: length=574, L/Lavg=0.708, ra=0.0, reward=-0.292
Completion 3: length=729, L/Lavg=0.899, ra=1.0, reward=0.000
------------- 16-17-45-30-988456 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
{'modal_conflict': tensor([True], device='cuda:0'), 'visual_anomaly': tensor([False], device='cuda:0'), 'audio_anomaly': tensor([False], device='cuda:0'), 'visual_boost': tensor([-0.0117], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'audio_boost': tensor([-0.0469], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:0', dtype=torch.int32), 'confidences': {'full': tensor([0.9961], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9844], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.9492], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-17-45-57-075645 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:2'), 'visual_anomaly': tensor([True], device='cuda:2'), 'audio_anomaly': tensor([False], device='cuda:2'), 'visual_boost': tensor([0.2148], device='cuda:2', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'audio_boost': tensor([-0.0078], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:2', dtype=torch.int32), 'confidences': {'full': tensor([0.7773], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9922], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.7695], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 624.75
Completion 0: length=662, L/Lavg=1.060, ra=0.0, reward=0.000
Completion 1: length=671, L/Lavg=1.074, ra=0.0, reward=0.000
Completion 2: length=442, L/Lavg=0.707, ra=0.0, reward=0.000
Completion 3: length=724, L/Lavg=1.159, ra=0.0, reward=0.000
------------- 16-17-45-57-128657 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:3'), 'visual_anomaly': tensor([True], device='cuda:3'), 'audio_anomaly': tensor([False], device='cuda:3'), 'visual_boost': tensor([0.1406], device='cuda:3', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'audio_boost': tensor([-0.2109], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:3', dtype=torch.int32), 'confidences': {'full': tensor([0.8438], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9844], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.6328], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 932.5
Completion 0: length=913, L/Lavg=0.979, ra=0.0, reward=0.000
Completion 1: length=1030, L/Lavg=1.105, ra=0.0, reward=0.000
Completion 2: length=811, L/Lavg=0.870, ra=0.0, reward=0.000
Completion 3: length=976, L/Lavg=1.047, ra=0.0, reward=0.000
------------- 16-17-45-57-136028 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:1'), 'visual_anomaly': tensor([False], device='cuda:1'), 'audio_anomaly': tensor([True], device='cuda:1'), 'visual_boost': tensor([-0.1484], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'audio_boost': tensor([0.2188], device='cuda:1', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:1', dtype=torch.int32), 'confidences': {'full': tensor([0.7773], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.6289], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.9961], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 686.0
Completion 0: length=763, L/Lavg=1.112, ra=0.0, reward=0.000
Completion 1: length=729, L/Lavg=1.063, ra=0.0, reward=0.000
Completion 2: length=776, L/Lavg=1.131, ra=1.0, reward=-0.131
Completion 3: length=476, L/Lavg=0.694, ra=0.0, reward=0.000
------------- 16-17-45-57-478323 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
{'modal_conflict': tensor([True], device='cuda:0'), 'visual_anomaly': tensor([False], device='cuda:0'), 'audio_anomaly': tensor([False], device='cuda:0'), 'visual_boost': tensor([-0.1367], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'audio_boost': tensor([0.0625], device='cuda:0', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:0', dtype=torch.int32), 'confidences': {'full': tensor([0.9375], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.8008], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([1.], device='cuda:0', dtype=torch.bfloat16, grad_fn=<StackBackward0>)}}
Average Length (Lavg): 888.75
Completion 0: length=684, L/Lavg=0.770, ra=0.0, reward=0.000
Completion 1: length=828, L/Lavg=0.932, ra=1.0, reward=0.068
Completion 2: length=872, L/Lavg=0.981, ra=1.0, reward=0.019
Completion 3: length=1171, L/Lavg=1.318, ra=1.0, reward=-0.318
------------- 16-17-46-18-824396 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:3'), 'visual_anomaly': tensor([False], device='cuda:3'), 'audio_anomaly': tensor([False], device='cuda:3'), 'visual_boost': tensor([-0.8281], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'audio_boost': tensor([0.0312], device='cuda:3', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:3', dtype=torch.int32), 'confidences': {'full': tensor([0.8672], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.0381], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.8984], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 599.25
Completion 0: length=591, L/Lavg=0.986, ra=0.0, reward=0.000
Completion 1: length=552, L/Lavg=0.921, ra=0.0, reward=0.000
Completion 2: length=801, L/Lavg=1.337, ra=0.0, reward=0.000
Completion 3: length=453, L/Lavg=0.756, ra=0.0, reward=0.000
------------- 16-17-46-18-825345 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:1'), 'visual_anomaly': tensor([False], device='cuda:1'), 'audio_anomaly': tensor([False], device='cuda:1'), 'visual_boost': tensor([0.0195], device='cuda:1', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'audio_boost': tensor([0.0273], device='cuda:1', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:1', dtype=torch.int32), 'confidences': {'full': tensor([0.9688], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9883], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.9961], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 825.25
Completion 0: length=1122, L/Lavg=1.360, ra=0.0, reward=0.000
Completion 1: length=633, L/Lavg=0.767, ra=0.0, reward=0.000
Completion 2: length=727, L/Lavg=0.881, ra=1.0, reward=0.119
Completion 3: length=819, L/Lavg=0.992, ra=0.0, reward=0.000
------------- 16-17-46-18-825741 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:2'), 'visual_anomaly': tensor([False], device='cuda:2'), 'audio_anomaly': tensor([False], device='cuda:2'), 'visual_boost': tensor([-0.1875], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'audio_boost': tensor([-0.1992], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:2', dtype=torch.int32), 'confidences': {'full': tensor([0.9961], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.8086], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.7969], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 960.25
Completion 0: length=1121, L/Lavg=1.167, ra=1.0, reward=-0.167
Completion 1: length=1008, L/Lavg=1.050, ra=0.0, reward=0.000
Completion 2: length=1049, L/Lavg=1.092, ra=0.0, reward=0.000
Completion 3: length=663, L/Lavg=0.690, ra=0.0, reward=0.000
------------- 16-17-46-18-900053 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:0'), 'visual_anomaly': tensor([False], device='cuda:0'), 'audio_anomaly': tensor([True], device='cuda:0'), 'visual_boost': tensor([0.0469], device='cuda:0', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'audio_boost': tensor([0.1055], device='cuda:0', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:0', dtype=torch.int32), 'confidences': {'full': tensor([0.8633], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9102], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.9688], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 1123.5
Completion 0: length=906, L/Lavg=0.806, ra=0.0, reward=0.000
Completion 1: length=1112, L/Lavg=0.990, ra=1.0, reward=0.010
Completion 2: length=1100, L/Lavg=0.979, ra=1.0, reward=0.021
Completion 3: length=1376, L/Lavg=1.225, ra=0.0, reward=0.000
------------- 16-17-46-35-299557 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:2'), 'visual_anomaly': tensor([False], device='cuda:2'), 'audio_anomaly': tensor([False], device='cuda:2'), 'visual_boost': tensor([0.0508], device='cuda:2', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'audio_boost': tensor([0.0625], device='cuda:2', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:2', dtype=torch.int32), 'confidences': {'full': tensor([0.9297], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9805], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.9922], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 677.0
Completion 0: length=581, L/Lavg=0.858, ra=0.0, reward=0.000
Completion 1: length=417, L/Lavg=0.616, ra=1.0, reward=0.384
Completion 2: length=826, L/Lavg=1.220, ra=1.0, reward=-0.220
Completion 3: length=884, L/Lavg=1.306, ra=0.0, reward=0.000
------------- 16-17-46-35-305542 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:3'), 'visual_anomaly': tensor([False], device='cuda:3'), 'audio_anomaly': tensor([False], device='cuda:3'), 'visual_boost': tensor([-0.0078], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'audio_boost': tensor([-0.0820], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:3', dtype=torch.int32), 'confidences': {'full': tensor([0.9570], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9492], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.8750], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 951.5
Completion 0: length=851, L/Lavg=0.894, ra=0.0, reward=0.000
Completion 1: length=1344, L/Lavg=1.413, ra=0.0, reward=0.000
Completion 2: length=762, L/Lavg=0.801, ra=0.0, reward=0.000
Completion 3: length=849, L/Lavg=0.892, ra=0.0, reward=0.000
------------- 16-17-46-35-311891 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
{'modal_conflict': tensor([True], device='cuda:0'), 'visual_anomaly': tensor([False], device='cuda:0'), 'audio_anomaly': tensor([False], device='cuda:0'), 'visual_boost': tensor([-0.1523], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'audio_boost': tensor([-0.0195], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:0', dtype=torch.int32), 'confidences': {'full': tensor([0.8477], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.6953], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.8281], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 700.0
Completion 0: length=835, L/Lavg=1.193, ra=1.0, reward=-0.193
Completion 1: length=824, L/Lavg=1.177, ra=1.0, reward=-0.177
Completion 2: length=583, L/Lavg=0.833, ra=0.0, reward=0.000
Completion 3: length=558, L/Lavg=0.797, ra=0.0, reward=0.000
------------- 16-17-46-35-316629 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
{'modal_conflict': tensor([True], device='cuda:1'), 'visual_anomaly': tensor([False], device='cuda:1'), 'audio_anomaly': tensor([False], device='cuda:1'), 'visual_boost': tensor([-0.1836], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'audio_boost': tensor([-0.1562], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:1', dtype=torch.int32), 'confidences': {'full': tensor([0.7695], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.5859], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.6133], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 901.0
Completion 0: length=617, L/Lavg=0.685, ra=1.0, reward=0.315
Completion 1: length=1130, L/Lavg=1.254, ra=1.0, reward=-0.254
Completion 2: length=995, L/Lavg=1.104, ra=0.0, reward=0.000
Completion 3: length=862, L/Lavg=0.957, ra=0.0, reward=0.000
------------- 16-17-46-58-438318 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:2'), 'visual_anomaly': tensor([False], device='cuda:2'), 'audio_anomaly': tensor([True], device='cuda:2'), 'visual_boost': tensor([-0.1758], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'audio_boost': tensor([0.2031], device='cuda:2', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:2', dtype=torch.int32), 'confidences': {'full': tensor([0.7969], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.6211], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([1.], device='cuda:2', dtype=torch.bfloat16, grad_fn=<StackBackward0>)}}
Average Length (Lavg): 954.25
Completion 0: length=681, L/Lavg=0.714, ra=0.0, reward=0.000
Completion 1: length=908, L/Lavg=0.952, ra=0.0, reward=0.000
Completion 2: length=1524, L/Lavg=1.597, ra=1.0, reward=-0.597
Completion 3: length=704, L/Lavg=0.738, ra=0.0, reward=0.000
------------- 16-17-46-58-445701 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:1'), 'visual_anomaly': tensor([True], device='cuda:1'), 'audio_anomaly': tensor([True], device='cuda:1'), 'visual_boost': tensor([0.2344], device='cuda:1', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'audio_boost': tensor([0.1367], device='cuda:1', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:1', dtype=torch.int32), 'confidences': {'full': tensor([0.7656], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([1.], device='cuda:1', dtype=torch.bfloat16, grad_fn=<StackBackward0>), 'audio': tensor([0.9023], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 773.5
Completion 0: length=850, L/Lavg=1.099, ra=0.0, reward=0.000
Completion 1: length=778, L/Lavg=1.006, ra=0.0, reward=0.000
Completion 2: length=560, L/Lavg=0.724, ra=0.0, reward=0.000
Completion 3: length=906, L/Lavg=1.171, ra=0.0, reward=0.000
------------- 16-17-46-58-453790 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:0'), 'visual_anomaly': tensor([False], device='cuda:0'), 'audio_anomaly': tensor([False], device='cuda:0'), 'visual_boost': tensor([-0.5508], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'audio_boost': tensor([0.0195], device='cuda:0', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:0', dtype=torch.int32), 'confidences': {'full': tensor([0.8672], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.3164], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.8867], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 1136.0
Completion 0: length=1037, L/Lavg=0.913, ra=0.0, reward=0.000
Completion 1: length=1710, L/Lavg=1.505, ra=0.0, reward=0.000
Completion 2: length=894, L/Lavg=0.787, ra=0.0, reward=0.000
Completion 3: length=903, L/Lavg=0.795, ra=0.0, reward=0.000
------------- 16-17-46-58-454072 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
{'modal_conflict': tensor([True], device='cuda:3'), 'visual_anomaly': tensor([False], device='cuda:3'), 'audio_anomaly': tensor([False], device='cuda:3'), 'visual_boost': tensor([-0.0312], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'audio_boost': tensor([-0.3477], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:3', dtype=torch.int32), 'confidences': {'full': tensor([0.9336], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9023], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.5859], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 1019.0
Completion 0: length=942, L/Lavg=0.924, ra=0.0, reward=0.000
Completion 1: length=752, L/Lavg=0.738, ra=1.0, reward=0.262
Completion 2: length=1559, L/Lavg=1.530, ra=1.0, reward=-0.530
Completion 3: length=823, L/Lavg=0.808, ra=1.0, reward=0.192
------------- 16-17-47-18-863156 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Hard 
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
{'modal_conflict': tensor([False], device='cuda:3'), 'visual_anomaly': tensor([False], device='cuda:3'), 'audio_anomaly': tensor([False], device='cuda:3'), 'visual_boost': tensor([-0.0469], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'audio_boost': tensor([0.0039], device='cuda:3', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([0], device='cuda:3', dtype=torch.int32), 'confidences': {'full': tensor([0.9961], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9492], device='cuda:3', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([1.], device='cuda:3', dtype=torch.bfloat16, grad_fn=<StackBackward0>)}}
Average Length (Lavg): 1118.25
Completion 0: length=806, L/Lavg=0.721, ra=0.0, reward=-0.279
Completion 1: length=1533, L/Lavg=1.371, ra=1.0, reward=0.000
Completion 2: length=791, L/Lavg=0.707, ra=1.0, reward=0.000
Completion 3: length=1343, L/Lavg=1.201, ra=1.0, reward=0.000
------------- 16-17-47-18-879139 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:0'), 'visual_anomaly': tensor([False], device='cuda:0'), 'audio_anomaly': tensor([False], device='cuda:0'), 'visual_boost': tensor([-0.1211], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'audio_boost': tensor([-0.0625], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:0', dtype=torch.int32), 'confidences': {'full': tensor([0.9375], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.8164], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.8750], device='cuda:0', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 1135.75
Completion 0: length=826, L/Lavg=0.727, ra=0.0, reward=0.000
Completion 1: length=678, L/Lavg=0.597, ra=1.0, reward=0.403
Completion 2: length=1549, L/Lavg=1.364, ra=0.0, reward=0.000
Completion 3: length=1490, L/Lavg=1.312, ra=0.0, reward=0.000
------------- 16-17-47-18-885680 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:2'), 'visual_anomaly': tensor([True], device='cuda:2'), 'audio_anomaly': tensor([False], device='cuda:2'), 'visual_boost': tensor([0.2695], device='cuda:2', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'audio_boost': tensor([0.0234], device='cuda:2', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:2', dtype=torch.int32), 'confidences': {'full': tensor([0.7266], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9961], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.7500], device='cuda:2', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 832.5
Completion 0: length=862, L/Lavg=1.035, ra=0.0, reward=0.000
Completion 1: length=910, L/Lavg=1.093, ra=1.0, reward=-0.093
Completion 2: length=704, L/Lavg=0.846, ra=1.0, reward=0.154
Completion 3: length=854, L/Lavg=1.026, ra=0.0, reward=0.000
------------- 16-17-47-18-888658 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy 
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
{'modal_conflict': tensor([True], device='cuda:1'), 'visual_anomaly': tensor([False], device='cuda:1'), 'audio_anomaly': tensor([False], device='cuda:1'), 'visual_boost': tensor([0.0664], device='cuda:1', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'audio_boost': tensor([0.0664], device='cuda:1', dtype=torch.bfloat16, grad_fn=<SubBackward0>), 'difficulty_score': tensor([1], device='cuda:1', dtype=torch.int32), 'confidences': {'full': tensor([0.9297], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<MeanBackward1>), 'visual': tensor([0.9961], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>), 'audio': tensor([0.9961], device='cuda:1', dtype=torch.bfloat16,
       grad_fn=<StackBackward0>)}}
Average Length (Lavg): 800.5
Completion 0: length=875, L/Lavg=1.093, ra=0.0, reward=0.000
Completion 1: length=713, L/Lavg=0.891, ra=0.0, reward=0.000
Completion 2: length=870, L/Lavg=1.087, ra=0.0, reward=0.000
Completion 3: length=744, L/Lavg=0.929, ra=0.0, reward=0.000
