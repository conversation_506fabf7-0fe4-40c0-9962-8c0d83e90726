------------- 15-14-11-15-782676 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 752.25
Completion 0: length=582, L/Lavg=0.774, ra=0.0, reward=0.000
Completion 1: length=1264, L/Lavg=1.680, ra=0.0, reward=0.000
Completion 2: length=743, L/Lavg=0.988, ra=0.0, reward=0.000
Completion 3: length=420, L/Lavg=0.558, ra=0.0, reward=0.000
------------- 15-14-11-15-782877 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 811.25
Completion 0: length=1146, L/Lavg=1.413, ra=1.0, reward=0.000
Completion 1: length=796, L/Lavg=0.981, ra=0.0, reward=-0.019
Completion 2: length=574, L/Lavg=0.708, ra=0.0, reward=-0.292
Completion 3: length=729, L/Lavg=0.899, ra=1.0, reward=0.000
------------- 15-14-11-15-784294 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 15-14-11-15-785245 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=0.000
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.000
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.000
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=0.000
------------- 15-14-11-42-020056 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 932.5
Completion 0: length=913, L/Lavg=0.979, ra=0.0, reward=0.000
Completion 1: length=1030, L/Lavg=1.105, ra=0.0, reward=0.000
Completion 2: length=811, L/Lavg=0.870, ra=0.0, reward=0.000
Completion 3: length=976, L/Lavg=1.047, ra=0.0, reward=0.000
------------- 15-14-11-42-023734 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 624.75
Completion 0: length=662, L/Lavg=1.060, ra=0.0, reward=0.000
Completion 1: length=671, L/Lavg=1.074, ra=0.0, reward=0.000
Completion 2: length=442, L/Lavg=0.707, ra=0.0, reward=0.000
Completion 3: length=724, L/Lavg=1.159, ra=0.0, reward=0.000
------------- 15-14-11-42-150750 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 686.0
Completion 0: length=763, L/Lavg=1.112, ra=0.0, reward=0.000
Completion 1: length=729, L/Lavg=1.063, ra=0.0, reward=0.000
Completion 2: length=776, L/Lavg=1.131, ra=1.0, reward=-0.131
Completion 3: length=476, L/Lavg=0.694, ra=0.0, reward=0.000
------------- 15-14-11-42-445315 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 888.75
Completion 0: length=684, L/Lavg=0.770, ra=0.0, reward=0.000
Completion 1: length=828, L/Lavg=0.932, ra=1.0, reward=0.068
Completion 2: length=872, L/Lavg=0.981, ra=1.0, reward=0.019
Completion 3: length=1171, L/Lavg=1.318, ra=1.0, reward=-0.318
------------- 15-14-21-06-384648 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 15-14-21-06-385894 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 752.25
Completion 0: length=582, L/Lavg=0.774, ra=0.0, reward=0.000
Completion 1: length=1264, L/Lavg=1.680, ra=0.0, reward=0.000
Completion 2: length=743, L/Lavg=0.988, ra=0.0, reward=0.000
Completion 3: length=420, L/Lavg=0.558, ra=0.0, reward=0.000
------------- 15-14-21-06-391042 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=0.000
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.000
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.000
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=0.000
------------- 15-14-21-06-405887 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 811.25
Completion 0: length=1146, L/Lavg=1.413, ra=1.0, reward=0.000
Completion 1: length=796, L/Lavg=0.981, ra=0.0, reward=-0.019
Completion 2: length=574, L/Lavg=0.708, ra=0.0, reward=-0.292
Completion 3: length=729, L/Lavg=0.899, ra=1.0, reward=0.000
------------- 16-14-42-22-093110 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=0.000
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.000
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.000
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=0.000
------------- 16-14-42-22-100223 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-14-47-44-082617 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=0.000
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.000
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.000
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=0.000
------------- 16-14-47-44-084564 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-14-59-32-929374 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=0.000
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.000
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.000
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=0.000
------------- 16-14-59-32-936931 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-01-50-083578 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-01-50-091403 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=0.000
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.000
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.000
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=0.000
------------- 16-15-02-11-583141 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 960.75
Completion 0: length=1054, L/Lavg=1.097, ra=0.0, reward=0.000
Completion 1: length=595, L/Lavg=0.619, ra=0.0, reward=0.000
Completion 2: length=1085, L/Lavg=1.129, ra=0.0, reward=0.000
Completion 3: length=1109, L/Lavg=1.154, ra=0.0, reward=0.000
------------- 16-15-02-11-637490 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 991.25
Completion 0: length=960, L/Lavg=0.968, ra=1.0, reward=0.032
Completion 1: length=1433, L/Lavg=1.446, ra=1.0, reward=-0.446
Completion 2: length=766, L/Lavg=0.773, ra=0.0, reward=0.000
Completion 3: length=806, L/Lavg=0.813, ra=0.0, reward=0.000
------------- 16-15-05-09-285223 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-05-26-334630 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 908.25
Completion 0: length=902, L/Lavg=0.993, ra=0.0, reward=0.000
Completion 1: length=903, L/Lavg=0.994, ra=0.0, reward=0.000
Completion 2: length=734, L/Lavg=0.808, ra=0.0, reward=0.000
Completion 3: length=1094, L/Lavg=1.205, ra=1.0, reward=-0.205
------------- 16-15-05-39-765724 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 752.5
Completion 0: length=826, L/Lavg=1.098, ra=1.0, reward=-0.098
Completion 1: length=643, L/Lavg=0.854, ra=1.0, reward=0.146
Completion 2: length=723, L/Lavg=0.961, ra=1.0, reward=0.039
Completion 3: length=818, L/Lavg=1.087, ra=0.0, reward=0.000
------------- 16-15-05-54-369867 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 653.5
Completion 0: length=786, L/Lavg=1.203, ra=0.0, reward=0.000
Completion 1: length=599, L/Lavg=0.917, ra=0.0, reward=0.000
Completion 2: length=501, L/Lavg=0.767, ra=0.0, reward=0.000
Completion 3: length=728, L/Lavg=1.114, ra=0.0, reward=0.000
------------- 16-15-06-07-612575 New Length Reward Debug -------------
Group Accuracy: 1.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 927.25
Completion 0: length=676, L/Lavg=0.729, ra=1.0, reward=0.000
Completion 1: length=980, L/Lavg=1.057, ra=1.0, reward=0.000
Completion 2: length=746, L/Lavg=0.805, ra=1.0, reward=0.000
Completion 3: length=1307, L/Lavg=1.410, ra=1.0, reward=0.000
------------- 16-15-06-19-013214 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 764.25
Completion 0: length=804, L/Lavg=1.052, ra=1.0, reward=-0.052
Completion 1: length=901, L/Lavg=1.179, ra=0.0, reward=0.000
Completion 2: length=803, L/Lavg=1.051, ra=0.0, reward=0.000
Completion 3: length=549, L/Lavg=0.718, ra=0.0, reward=0.000
------------- 16-15-06-30-389291 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 860.0
Completion 0: length=713, L/Lavg=0.829, ra=0.0, reward=0.000
Completion 1: length=1217, L/Lavg=1.415, ra=0.0, reward=0.000
Completion 2: length=799, L/Lavg=0.929, ra=0.0, reward=0.000
Completion 3: length=711, L/Lavg=0.827, ra=0.0, reward=0.000
------------- 16-15-11-26-553319 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-14-23-282625 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-14-40-796000 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 908.25
Completion 0: length=902, L/Lavg=0.993, ra=0.0, reward=0.000
Completion 1: length=903, L/Lavg=0.994, ra=0.0, reward=0.000
Completion 2: length=734, L/Lavg=0.808, ra=0.0, reward=0.000
Completion 3: length=1094, L/Lavg=1.205, ra=1.0, reward=-0.205
------------- 16-15-14-55-145610 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 852.75
Completion 0: length=933, L/Lavg=1.094, ra=0.0, reward=0.000
Completion 1: length=1058, L/Lavg=1.241, ra=0.0, reward=0.000
Completion 2: length=763, L/Lavg=0.895, ra=1.0, reward=0.105
Completion 3: length=657, L/Lavg=0.770, ra=1.0, reward=0.230
------------- 16-15-15-09-515284 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 858.25
Completion 0: length=1126, L/Lavg=1.312, ra=0.0, reward=0.000
Completion 1: length=1101, L/Lavg=1.283, ra=0.0, reward=0.000
Completion 2: length=612, L/Lavg=0.713, ra=1.0, reward=0.287
Completion 3: length=594, L/Lavg=0.692, ra=0.0, reward=0.000
------------- 16-15-16-18-832229 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-33-13-933327 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-36-22-856687 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-39-46-199269 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-41-54-327938 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-51-30-470287 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-54-41-836377 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-54-58-939433 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 908.25
Completion 0: length=902, L/Lavg=0.993, ra=0.0, reward=0.000
Completion 1: length=903, L/Lavg=0.994, ra=0.0, reward=0.000
Completion 2: length=734, L/Lavg=0.808, ra=0.0, reward=0.000
Completion 3: length=1094, L/Lavg=1.205, ra=1.0, reward=-0.205
------------- 16-16-07-27-410772 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-16-13-02-983686 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-16-14-28-998768 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-16-31-44-681583 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-16-32-05-238933 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 908.25
Completion 0: length=902, L/Lavg=0.993, ra=0.0, reward=0.000
Completion 1: length=903, L/Lavg=0.994, ra=0.0, reward=0.000
Completion 2: length=734, L/Lavg=0.808, ra=0.0, reward=0.000
Completion 3: length=1094, L/Lavg=1.205, ra=1.0, reward=-0.205
------------- 16-16-32-22-990123 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 768.75
Completion 0: length=853, L/Lavg=1.110, ra=1.0, reward=-0.110
Completion 1: length=817, L/Lavg=1.063, ra=0.0, reward=0.000
Completion 2: length=612, L/Lavg=0.796, ra=1.0, reward=0.204
Completion 3: length=793, L/Lavg=1.032, ra=1.0, reward=-0.032
------------- 16-16-32-36-178268 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 635.25
Completion 0: length=544, L/Lavg=0.856, ra=0.0, reward=0.000
Completion 1: length=658, L/Lavg=1.036, ra=0.0, reward=0.000
Completion 2: length=511, L/Lavg=0.804, ra=0.0, reward=0.000
Completion 3: length=828, L/Lavg=1.303, ra=0.0, reward=0.000
------------- 16-16-35-03-711037 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-16-36-19-092862 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-16-36-36-302795 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 908.25
Completion 0: length=902, L/Lavg=0.993, ra=0.0, reward=0.000
Completion 1: length=903, L/Lavg=0.994, ra=0.0, reward=0.000
Completion 2: length=734, L/Lavg=0.808, ra=0.0, reward=0.000
Completion 3: length=1094, L/Lavg=1.205, ra=1.0, reward=-0.205
------------- 16-16-36-50-526240 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1067.25
Completion 0: length=1633, L/Lavg=1.530, ra=0.0, reward=0.000
Completion 1: length=1041, L/Lavg=0.975, ra=1.0, reward=0.025
Completion 2: length=612, L/Lavg=0.573, ra=1.0, reward=0.427
Completion 3: length=983, L/Lavg=0.921, ra=0.0, reward=0.000
------------- 16-16-37-05-500871 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 656.25
Completion 0: length=505, L/Lavg=0.770, ra=0.0, reward=0.000
Completion 1: length=720, L/Lavg=1.097, ra=0.0, reward=0.000
Completion 2: length=601, L/Lavg=0.916, ra=0.0, reward=0.000
Completion 3: length=799, L/Lavg=1.218, ra=0.0, reward=0.000
------------- 16-16-37-21-257009 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 891.25
Completion 0: length=1191, L/Lavg=1.336, ra=1.0, reward=-0.336
Completion 1: length=825, L/Lavg=0.926, ra=1.0, reward=0.074
Completion 2: length=452, L/Lavg=0.507, ra=0.0, reward=0.000
Completion 3: length=1097, L/Lavg=1.231, ra=0.0, reward=0.000
------------- 16-16-37-31-610035 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 779.75
Completion 0: length=838, L/Lavg=1.075, ra=0.0, reward=0.075
Completion 1: length=728, L/Lavg=0.934, ra=0.0, reward=-0.066
Completion 2: length=953, L/Lavg=1.222, ra=0.0, reward=0.222
Completion 3: length=600, L/Lavg=0.769, ra=0.0, reward=-0.231
------------- 16-16-37-42-515947 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 674.25
Completion 0: length=633, L/Lavg=0.939, ra=0.0, reward=-0.061
Completion 1: length=759, L/Lavg=1.126, ra=0.0, reward=0.126
Completion 2: length=920, L/Lavg=1.364, ra=0.0, reward=0.364
Completion 3: length=385, L/Lavg=0.571, ra=0.0, reward=-0.429
------------- 16-16-37-55-049738 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 712.5
Completion 0: length=895, L/Lavg=1.256, ra=0.0, reward=0.000
Completion 1: length=656, L/Lavg=0.921, ra=0.0, reward=0.000
Completion 2: length=701, L/Lavg=0.984, ra=0.0, reward=0.000
Completion 3: length=598, L/Lavg=0.839, ra=0.0, reward=0.000
------------- 16-16-38-09-100338 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 699.5
Completion 0: length=740, L/Lavg=1.058, ra=0.0, reward=0.000
Completion 1: length=701, L/Lavg=1.002, ra=0.0, reward=0.000
Completion 2: length=782, L/Lavg=1.118, ra=1.0, reward=-0.118
Completion 3: length=575, L/Lavg=0.822, ra=0.0, reward=0.000
------------- 16-16-38-19-196720 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 680.0
Completion 0: length=699, L/Lavg=1.028, ra=0.0, reward=0.000
Completion 1: length=557, L/Lavg=0.819, ra=0.0, reward=0.000
Completion 2: length=756, L/Lavg=1.112, ra=0.0, reward=0.000
Completion 3: length=708, L/Lavg=1.041, ra=0.0, reward=0.000
------------- 16-16-38-32-581157 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 776.5
Completion 0: length=657, L/Lavg=0.846, ra=1.0, reward=0.154
Completion 1: length=835, L/Lavg=1.075, ra=0.0, reward=0.000
Completion 2: length=910, L/Lavg=1.172, ra=0.0, reward=0.000
Completion 3: length=704, L/Lavg=0.907, ra=0.0, reward=0.000
------------- 16-16-38-45-204304 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 688.0
Completion 0: length=632, L/Lavg=0.919, ra=1.0, reward=0.081
Completion 1: length=738, L/Lavg=1.073, ra=1.0, reward=-0.073
Completion 2: length=802, L/Lavg=1.166, ra=0.0, reward=0.000
Completion 3: length=580, L/Lavg=0.843, ra=0.0, reward=0.000
------------- 16-16-38-58-599481 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 963.25
Completion 0: length=1404, L/Lavg=1.458, ra=1.0, reward=-0.458
Completion 1: length=574, L/Lavg=0.596, ra=0.0, reward=0.000
Completion 2: length=1357, L/Lavg=1.409, ra=1.0, reward=-0.409
Completion 3: length=518, L/Lavg=0.538, ra=0.0, reward=0.000
------------- 16-16-39-13-246995 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 707.0
Completion 0: length=886, L/Lavg=1.253, ra=1.0, reward=0.000
Completion 1: length=700, L/Lavg=0.990, ra=1.0, reward=0.000
Completion 2: length=644, L/Lavg=0.911, ra=0.0, reward=-0.089
Completion 3: length=598, L/Lavg=0.846, ra=0.0, reward=-0.154
------------- 16-16-39-25-125683 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.5
Completion 0: length=941, L/Lavg=1.112, ra=0.0, reward=0.000
Completion 1: length=712, L/Lavg=0.841, ra=0.0, reward=0.000
Completion 2: length=683, L/Lavg=0.807, ra=1.0, reward=0.193
Completion 3: length=1050, L/Lavg=1.240, ra=0.0, reward=0.000
------------- 16-16-39-41-897742 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 781.0
Completion 0: length=996, L/Lavg=1.275, ra=0.0, reward=0.000
Completion 1: length=1057, L/Lavg=1.353, ra=0.0, reward=0.000
Completion 2: length=613, L/Lavg=0.785, ra=0.0, reward=0.000
Completion 3: length=458, L/Lavg=0.586, ra=0.0, reward=0.000
------------- 16-16-39-59-716487 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1087.5
Completion 0: length=840, L/Lavg=0.772, ra=0.0, reward=0.000
Completion 1: length=1586, L/Lavg=1.458, ra=0.0, reward=0.000
Completion 2: length=1298, L/Lavg=1.194, ra=0.0, reward=0.000
Completion 3: length=626, L/Lavg=0.576, ra=0.0, reward=0.000
------------- 16-16-40-11-777883 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 831.0
Completion 0: length=613, L/Lavg=0.738, ra=0.0, reward=0.000
Completion 1: length=556, L/Lavg=0.669, ra=0.0, reward=0.000
Completion 2: length=1354, L/Lavg=1.629, ra=0.0, reward=0.000
Completion 3: length=801, L/Lavg=0.964, ra=0.0, reward=0.000
------------- 16-16-40-28-326622 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 729.0
Completion 0: length=740, L/Lavg=1.015, ra=0.0, reward=0.015
Completion 1: length=817, L/Lavg=1.121, ra=0.0, reward=0.121
Completion 2: length=704, L/Lavg=0.966, ra=0.0, reward=-0.034
Completion 3: length=655, L/Lavg=0.898, ra=0.0, reward=-0.102
------------- 16-16-40-41-409905 New Length Reward Debug -------------
Group Accuracy: 1.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 764.25
Completion 0: length=1140, L/Lavg=1.492, ra=1.0, reward=-0.492
Completion 1: length=750, L/Lavg=0.981, ra=1.0, reward=0.019
Completion 2: length=465, L/Lavg=0.608, ra=1.0, reward=0.392
Completion 3: length=702, L/Lavg=0.919, ra=1.0, reward=0.081
------------- 16-16-40-56-466819 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1275.0
Completion 0: length=734, L/Lavg=0.576, ra=1.0, reward=0.424
Completion 1: length=1272, L/Lavg=0.998, ra=0.0, reward=0.000
Completion 2: length=1782, L/Lavg=1.398, ra=0.0, reward=0.000
Completion 3: length=1312, L/Lavg=1.029, ra=1.0, reward=-0.029
------------- 16-16-41-09-161400 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 661.0
Completion 0: length=735, L/Lavg=1.112, ra=0.0, reward=0.000
Completion 1: length=647, L/Lavg=0.979, ra=0.0, reward=0.000
Completion 2: length=608, L/Lavg=0.920, ra=0.0, reward=0.000
Completion 3: length=654, L/Lavg=0.989, ra=0.0, reward=0.000
------------- 16-16-41-22-854835 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 843.5
Completion 0: length=912, L/Lavg=1.081, ra=0.0, reward=0.000
Completion 1: length=784, L/Lavg=0.929, ra=0.0, reward=0.000
Completion 2: length=790, L/Lavg=0.937, ra=0.0, reward=0.000
Completion 3: length=888, L/Lavg=1.053, ra=0.0, reward=0.000
------------- 16-16-41-35-836833 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 867.25
Completion 0: length=606, L/Lavg=0.699, ra=1.0, reward=0.000
Completion 1: length=1368, L/Lavg=1.577, ra=1.0, reward=0.000
Completion 2: length=750, L/Lavg=0.865, ra=0.0, reward=-0.135
Completion 3: length=745, L/Lavg=0.859, ra=1.0, reward=0.000
------------- 16-16-41-48-539970 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 767.5
Completion 0: length=556, L/Lavg=0.724, ra=1.0, reward=0.276
Completion 1: length=708, L/Lavg=0.922, ra=0.0, reward=0.000
Completion 2: length=584, L/Lavg=0.761, ra=1.0, reward=0.239
Completion 3: length=1222, L/Lavg=1.592, ra=0.0, reward=0.000
------------- 16-16-42-08-800041 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 627.75
Completion 0: length=874, L/Lavg=1.392, ra=0.0, reward=0.000
Completion 1: length=434, L/Lavg=0.691, ra=0.0, reward=0.000
Completion 2: length=621, L/Lavg=0.989, ra=0.0, reward=0.000
Completion 3: length=582, L/Lavg=0.927, ra=0.0, reward=0.000
------------- 16-16-42-21-047145 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 733.5
Completion 0: length=798, L/Lavg=1.088, ra=1.0, reward=0.000
Completion 1: length=497, L/Lavg=0.678, ra=0.0, reward=-0.322
Completion 2: length=844, L/Lavg=1.151, ra=1.0, reward=0.000
Completion 3: length=795, L/Lavg=1.084, ra=1.0, reward=0.000
------------- 16-16-42-37-018405 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 787.25
Completion 0: length=844, L/Lavg=1.072, ra=0.0, reward=0.000
Completion 1: length=516, L/Lavg=0.655, ra=0.0, reward=0.000
Completion 2: length=991, L/Lavg=1.259, ra=0.0, reward=0.000
Completion 3: length=798, L/Lavg=1.014, ra=1.0, reward=-0.014
------------- 16-16-42-51-334682 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1332.75
Completion 0: length=1476, L/Lavg=1.107, ra=0.0, reward=0.000
Completion 1: length=1705, L/Lavg=1.279, ra=0.0, reward=0.000
Completion 2: length=725, L/Lavg=0.544, ra=0.0, reward=0.000
Completion 3: length=1425, L/Lavg=1.069, ra=0.0, reward=0.000
------------- 16-16-43-05-836001 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1112.25
Completion 0: length=908, L/Lavg=0.816, ra=0.0, reward=0.000
Completion 1: length=1011, L/Lavg=0.909, ra=1.0, reward=0.091
Completion 2: length=1343, L/Lavg=1.207, ra=1.0, reward=-0.207
Completion 3: length=1187, L/Lavg=1.067, ra=0.0, reward=0.000
------------- 16-16-43-19-817794 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 733.75
Completion 0: length=327, L/Lavg=0.446, ra=0.0, reward=0.000
Completion 1: length=513, L/Lavg=0.699, ra=0.0, reward=0.000
Completion 2: length=1616, L/Lavg=2.202, ra=1.0, reward=-1.202
Completion 3: length=479, L/Lavg=0.653, ra=1.0, reward=0.347
------------- 16-16-43-33-324427 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 663.0
Completion 0: length=563, L/Lavg=0.849, ra=0.0, reward=0.000
Completion 1: length=844, L/Lavg=1.273, ra=1.0, reward=-0.273
Completion 2: length=680, L/Lavg=1.026, ra=1.0, reward=-0.026
Completion 3: length=565, L/Lavg=0.852, ra=1.0, reward=0.148
------------- 16-16-43-44-475018 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 572.25
Completion 0: length=462, L/Lavg=0.807, ra=0.0, reward=0.000
Completion 1: length=733, L/Lavg=1.281, ra=0.0, reward=0.000
Completion 2: length=505, L/Lavg=0.882, ra=0.0, reward=0.000
Completion 3: length=589, L/Lavg=1.029, ra=0.0, reward=0.000
------------- 16-16-43-59-476269 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1118.5
Completion 0: length=432, L/Lavg=0.386, ra=0.0, reward=0.000
Completion 1: length=1493, L/Lavg=1.335, ra=0.0, reward=0.000
Completion 2: length=1550, L/Lavg=1.386, ra=0.0, reward=0.000
Completion 3: length=999, L/Lavg=0.893, ra=0.0, reward=0.000
------------- 16-16-44-13-123085 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 656.25
Completion 0: length=707, L/Lavg=1.077, ra=0.0, reward=0.000
Completion 1: length=462, L/Lavg=0.704, ra=1.0, reward=0.296
Completion 2: length=775, L/Lavg=1.181, ra=0.0, reward=0.000
Completion 3: length=681, L/Lavg=1.038, ra=0.0, reward=0.000
------------- 16-16-44-26-593643 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 819.5
Completion 0: length=989, L/Lavg=1.207, ra=1.0, reward=-0.207
Completion 1: length=765, L/Lavg=0.933, ra=0.0, reward=0.000
Completion 2: length=900, L/Lavg=1.098, ra=0.0, reward=0.000
Completion 3: length=624, L/Lavg=0.761, ra=1.0, reward=0.239
------------- 16-16-44-41-221548 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1076.75
Completion 0: length=947, L/Lavg=0.879, ra=0.0, reward=0.000
Completion 1: length=708, L/Lavg=0.658, ra=0.0, reward=0.000
Completion 2: length=1180, L/Lavg=1.096, ra=0.0, reward=0.000
Completion 3: length=1472, L/Lavg=1.367, ra=0.0, reward=0.000
------------- 16-16-44-55-666790 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 896.0
Completion 0: length=787, L/Lavg=0.878, ra=0.0, reward=-0.122
Completion 1: length=646, L/Lavg=0.721, ra=0.0, reward=-0.279
Completion 2: length=1385, L/Lavg=1.546, ra=0.0, reward=0.546
Completion 3: length=766, L/Lavg=0.855, ra=0.0, reward=-0.145
------------- 16-16-45-09-257984 New Length Reward Debug -------------
Group Accuracy: 1.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 653.25
Completion 0: length=867, L/Lavg=1.327, ra=1.0, reward=0.000
Completion 1: length=442, L/Lavg=0.677, ra=1.0, reward=0.000
Completion 2: length=736, L/Lavg=1.127, ra=1.0, reward=0.000
Completion 3: length=568, L/Lavg=0.869, ra=1.0, reward=0.000
------------- 16-16-45-21-782405 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 994.25
Completion 0: length=1153, L/Lavg=1.160, ra=0.0, reward=0.000
Completion 1: length=1227, L/Lavg=1.234, ra=1.0, reward=-0.234
Completion 2: length=727, L/Lavg=0.731, ra=0.0, reward=0.000
Completion 3: length=870, L/Lavg=0.875, ra=1.0, reward=0.125
------------- 16-16-48-47-764468 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-16-49-07-068649 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 908.25
Completion 0: length=902, L/Lavg=0.993, ra=0.0, reward=0.000
Completion 1: length=903, L/Lavg=0.994, ra=0.0, reward=0.000
Completion 2: length=734, L/Lavg=0.808, ra=0.0, reward=0.000
Completion 3: length=1094, L/Lavg=1.205, ra=1.0, reward=-0.205
------------- 16-16-53-45-110035 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-16-59-52-207929 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-17-00-09-459609 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 908.25
Completion 0: length=902, L/Lavg=0.993, ra=0.0, reward=0.000
Completion 1: length=903, L/Lavg=0.994, ra=0.0, reward=0.000
Completion 2: length=734, L/Lavg=0.808, ra=0.0, reward=0.000
Completion 3: length=1094, L/Lavg=1.205, ra=1.0, reward=-0.205
------------- 16-17-00-24-459286 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 791.5
Completion 0: length=878, L/Lavg=1.109, ra=1.0, reward=-0.109
Completion 1: length=594, L/Lavg=0.750, ra=1.0, reward=0.250
Completion 2: length=716, L/Lavg=0.905, ra=1.0, reward=0.095
Completion 3: length=978, L/Lavg=1.236, ra=0.0, reward=0.000
------------- 16-17-02-37-664030 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 811.25
Completion 0: length=1146, L/Lavg=1.413, ra=1.0, reward=0.000
Completion 1: length=796, L/Lavg=0.981, ra=0.0, reward=-0.019
Completion 2: length=574, L/Lavg=0.708, ra=0.0, reward=-0.292
Completion 3: length=729, L/Lavg=0.899, ra=1.0, reward=0.000
------------- 16-17-02-37-664917 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=-0.117
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.091
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.056
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=-0.030
------------- 16-17-02-37-665531 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 752.25
Completion 0: length=582, L/Lavg=0.774, ra=0.0, reward=-0.226
Completion 1: length=1264, L/Lavg=1.680, ra=0.0, reward=0.680
Completion 2: length=743, L/Lavg=0.988, ra=0.0, reward=-0.012
Completion 3: length=420, L/Lavg=0.558, ra=0.0, reward=-0.442
------------- 16-17-02-37-666471 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-17-04-49-556278 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 811.25
Completion 0: length=1146, L/Lavg=1.413, ra=1.0, reward=0.000
Completion 1: length=796, L/Lavg=0.981, ra=0.0, reward=-0.019
Completion 2: length=574, L/Lavg=0.708, ra=0.0, reward=-0.292
Completion 3: length=729, L/Lavg=0.899, ra=1.0, reward=0.000
------------- 16-17-04-49-556912 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=-0.117
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.091
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.056
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=-0.030
------------- 16-17-04-49-558524 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-17-04-49-559600 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 752.25
Completion 0: length=582, L/Lavg=0.774, ra=0.0, reward=-0.226
Completion 1: length=1264, L/Lavg=1.680, ra=0.0, reward=0.680
Completion 2: length=743, L/Lavg=0.988, ra=0.0, reward=-0.012
Completion 3: length=420, L/Lavg=0.558, ra=0.0, reward=-0.442
------------- 16-17-05-13-411009 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 686.0
Completion 0: length=763, L/Lavg=1.112, ra=0.0, reward=0.000
Completion 1: length=729, L/Lavg=1.063, ra=0.0, reward=0.000
Completion 2: length=776, L/Lavg=1.131, ra=1.0, reward=-0.131
Completion 3: length=476, L/Lavg=0.694, ra=0.0, reward=0.000
------------- 16-17-05-13-415240 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 932.5
Completion 0: length=913, L/Lavg=0.979, ra=0.0, reward=0.000
Completion 1: length=1030, L/Lavg=1.105, ra=0.0, reward=0.000
Completion 2: length=811, L/Lavg=0.870, ra=0.0, reward=0.000
Completion 3: length=976, L/Lavg=1.047, ra=0.0, reward=0.000
------------- 16-17-05-13-428908 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 888.75
Completion 0: length=684, L/Lavg=0.770, ra=0.0, reward=0.000
Completion 1: length=828, L/Lavg=0.932, ra=1.0, reward=0.068
Completion 2: length=872, L/Lavg=0.981, ra=1.0, reward=0.019
Completion 3: length=1171, L/Lavg=1.318, ra=1.0, reward=-0.318
------------- 16-17-05-13-456810 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 624.75
Completion 0: length=662, L/Lavg=1.060, ra=0.0, reward=0.000
Completion 1: length=671, L/Lavg=1.074, ra=0.0, reward=0.000
Completion 2: length=442, L/Lavg=0.707, ra=0.0, reward=0.000
Completion 3: length=724, L/Lavg=1.159, ra=0.0, reward=0.000
------------- 16-17-05-35-060286 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 719.75
Completion 0: length=801, L/Lavg=1.113, ra=0.0, reward=0.000
Completion 1: length=693, L/Lavg=0.963, ra=1.0, reward=0.037
Completion 2: length=737, L/Lavg=1.024, ra=0.0, reward=0.000
Completion 3: length=648, L/Lavg=0.900, ra=0.0, reward=0.000
------------- 16-17-05-35-084159 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 784.0
Completion 0: length=988, L/Lavg=1.260, ra=0.0, reward=0.000
Completion 1: length=608, L/Lavg=0.776, ra=0.0, reward=0.000
Completion 2: length=747, L/Lavg=0.953, ra=0.0, reward=0.000
Completion 3: length=793, L/Lavg=1.011, ra=0.0, reward=0.000
------------- 16-17-05-35-084904 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1267.5
Completion 0: length=1520, L/Lavg=1.199, ra=0.0, reward=0.000
Completion 1: length=912, L/Lavg=0.720, ra=0.0, reward=0.000
Completion 2: length=1513, L/Lavg=1.194, ra=0.0, reward=0.000
Completion 3: length=1125, L/Lavg=0.888, ra=0.0, reward=0.000
------------- 16-17-05-35-085634 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 909.25
Completion 0: length=565, L/Lavg=0.621, ra=0.0, reward=0.000
Completion 1: length=721, L/Lavg=0.793, ra=0.0, reward=0.000
Completion 2: length=1010, L/Lavg=1.111, ra=0.0, reward=0.000
Completion 3: length=1341, L/Lavg=1.475, ra=0.0, reward=0.000
------------- 16-17-05-55-484545 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 1100.5
Completion 0: length=1144, L/Lavg=1.040, ra=1.0, reward=-0.040
Completion 1: length=892, L/Lavg=0.811, ra=1.0, reward=0.189
Completion 2: length=1125, L/Lavg=1.022, ra=0.0, reward=0.000
Completion 3: length=1241, L/Lavg=1.128, ra=1.0, reward=-0.128
------------- 16-17-05-55-494999 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 794.5
Completion 0: length=752, L/Lavg=0.947, ra=1.0, reward=0.053
Completion 1: length=484, L/Lavg=0.609, ra=1.0, reward=0.391
Completion 2: length=773, L/Lavg=0.973, ra=0.0, reward=0.000
Completion 3: length=1169, L/Lavg=1.471, ra=0.0, reward=0.000
------------- 16-17-05-55-496517 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 648.5
Completion 0: length=688, L/Lavg=1.061, ra=0.0, reward=0.000
Completion 1: length=743, L/Lavg=1.146, ra=0.0, reward=0.000
Completion 2: length=742, L/Lavg=1.144, ra=0.0, reward=0.000
Completion 3: length=421, L/Lavg=0.649, ra=0.0, reward=0.000
------------- 16-17-05-55-507333 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1099.5
Completion 0: length=1198, L/Lavg=1.090, ra=0.0, reward=0.090
Completion 1: length=1218, L/Lavg=1.108, ra=0.0, reward=0.108
Completion 2: length=1045, L/Lavg=0.950, ra=0.0, reward=-0.050
Completion 3: length=937, L/Lavg=0.852, ra=0.0, reward=-0.148
------------- 16-17-06-13-497270 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 854.5
Completion 0: length=791, L/Lavg=0.926, ra=0.0, reward=0.000
Completion 1: length=850, L/Lavg=0.995, ra=0.0, reward=0.000
Completion 2: length=953, L/Lavg=1.115, ra=0.0, reward=0.000
Completion 3: length=824, L/Lavg=0.964, ra=0.0, reward=0.000
------------- 16-17-06-13-511560 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 692.5
Completion 0: length=821, L/Lavg=1.186, ra=1.0, reward=0.000
Completion 1: length=733, L/Lavg=1.058, ra=1.0, reward=0.000
Completion 2: length=603, L/Lavg=0.871, ra=0.0, reward=-0.129
Completion 3: length=613, L/Lavg=0.885, ra=1.0, reward=0.000
------------- 16-17-06-13-512729 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 749.5
Completion 0: length=580, L/Lavg=0.774, ra=0.0, reward=0.000
Completion 1: length=975, L/Lavg=1.301, ra=0.0, reward=0.000
Completion 2: length=584, L/Lavg=0.779, ra=0.0, reward=0.000
Completion 3: length=859, L/Lavg=1.146, ra=0.0, reward=0.000
------------- 16-17-06-13-597538 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1243.5
Completion 0: length=736, L/Lavg=0.592, ra=0.0, reward=-0.408
Completion 1: length=1737, L/Lavg=1.397, ra=0.0, reward=0.397
Completion 2: length=1471, L/Lavg=1.183, ra=0.0, reward=0.183
Completion 3: length=1030, L/Lavg=0.828, ra=0.0, reward=-0.172
------------- 16-17-06-32-068040 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 852.25
Completion 0: length=1644, L/Lavg=1.929, ra=1.0, reward=-0.929
Completion 1: length=732, L/Lavg=0.859, ra=0.0, reward=0.000
Completion 2: length=495, L/Lavg=0.581, ra=1.0, reward=0.419
Completion 3: length=538, L/Lavg=0.631, ra=1.0, reward=0.369
------------- 16-17-06-32-078133 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 806.0
Completion 0: length=738, L/Lavg=0.916, ra=0.0, reward=0.000
Completion 1: length=960, L/Lavg=1.191, ra=0.0, reward=0.000
Completion 2: length=583, L/Lavg=0.723, ra=0.0, reward=0.000
Completion 3: length=943, L/Lavg=1.170, ra=0.0, reward=0.000
------------- 16-17-06-32-114861 New Length Reward Debug -------------
Group Accuracy: 1.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 977.25
Completion 0: length=1311, L/Lavg=1.342, ra=1.0, reward=0.000
Completion 1: length=779, L/Lavg=0.797, ra=1.0, reward=0.000
Completion 2: length=556, L/Lavg=0.569, ra=1.0, reward=0.000
Completion 3: length=1263, L/Lavg=1.292, ra=1.0, reward=0.000
------------- 16-17-06-32-115570 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 663.25
Completion 0: length=732, L/Lavg=1.104, ra=0.0, reward=0.104
Completion 1: length=533, L/Lavg=0.804, ra=0.0, reward=-0.196
Completion 2: length=760, L/Lavg=1.146, ra=1.0, reward=0.000
Completion 3: length=628, L/Lavg=0.947, ra=1.0, reward=0.000
------------- 16-17-06-51-942538 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 773.25
Completion 0: length=819, L/Lavg=1.059, ra=0.0, reward=0.000
Completion 1: length=703, L/Lavg=0.909, ra=0.0, reward=0.000
Completion 2: length=977, L/Lavg=1.263, ra=0.0, reward=0.000
Completion 3: length=594, L/Lavg=0.768, ra=0.0, reward=0.000
------------- 16-17-06-51-943423 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1076.25
Completion 0: length=1532, L/Lavg=1.423, ra=1.0, reward=0.000
Completion 1: length=821, L/Lavg=0.763, ra=0.0, reward=-0.237
Completion 2: length=643, L/Lavg=0.597, ra=1.0, reward=0.000
Completion 3: length=1309, L/Lavg=1.216, ra=1.0, reward=0.000
------------- 16-17-06-51-978088 New Length Reward Debug -------------
Group Accuracy: 1.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 713.75
Completion 0: length=694, L/Lavg=0.972, ra=1.0, reward=0.028
Completion 1: length=781, L/Lavg=1.094, ra=1.0, reward=-0.094
Completion 2: length=749, L/Lavg=1.049, ra=1.0, reward=-0.049
Completion 3: length=631, L/Lavg=0.884, ra=1.0, reward=0.116
------------- 16-17-06-51-979051 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 950.25
Completion 0: length=926, L/Lavg=0.974, ra=1.0, reward=0.026
Completion 1: length=1594, L/Lavg=1.677, ra=1.0, reward=-0.677
Completion 2: length=789, L/Lavg=0.830, ra=0.0, reward=0.000
Completion 3: length=492, L/Lavg=0.518, ra=0.0, reward=0.000
------------- 16-17-07-50-448334 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=-0.117
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.091
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.056
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=-0.030
------------- 16-17-07-50-448621 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 752.25
Completion 0: length=582, L/Lavg=0.774, ra=0.0, reward=-0.226
Completion 1: length=1264, L/Lavg=1.680, ra=0.0, reward=0.680
Completion 2: length=743, L/Lavg=0.988, ra=0.0, reward=-0.012
Completion 3: length=420, L/Lavg=0.558, ra=0.0, reward=-0.442
------------- 16-17-07-50-448772 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 811.25
Completion 0: length=1146, L/Lavg=1.413, ra=1.0, reward=0.000
Completion 1: length=796, L/Lavg=0.981, ra=0.0, reward=-0.019
Completion 2: length=574, L/Lavg=0.708, ra=0.0, reward=-0.292
Completion 3: length=729, L/Lavg=0.899, ra=1.0, reward=0.000
------------- 16-17-07-50-449553 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-17-08-13-657590 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 888.75
Completion 0: length=684, L/Lavg=0.770, ra=0.0, reward=0.000
Completion 1: length=828, L/Lavg=0.932, ra=1.0, reward=0.068
Completion 2: length=872, L/Lavg=0.981, ra=1.0, reward=0.019
Completion 3: length=1171, L/Lavg=1.318, ra=1.0, reward=-0.318
------------- 16-17-08-13-700791 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 686.0
Completion 0: length=763, L/Lavg=1.112, ra=0.0, reward=0.000
Completion 1: length=729, L/Lavg=1.063, ra=0.0, reward=0.000
Completion 2: length=776, L/Lavg=1.131, ra=1.0, reward=-0.131
Completion 3: length=476, L/Lavg=0.694, ra=0.0, reward=0.000
------------- 16-17-08-13-772390 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 932.5
Completion 0: length=913, L/Lavg=0.979, ra=0.0, reward=0.000
Completion 1: length=1030, L/Lavg=1.105, ra=0.0, reward=0.000
Completion 2: length=811, L/Lavg=0.870, ra=0.0, reward=0.000
Completion 3: length=976, L/Lavg=1.047, ra=0.0, reward=0.000
------------- 16-17-08-13-796382 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 624.75
Completion 0: length=662, L/Lavg=1.060, ra=0.0, reward=0.000
Completion 1: length=671, L/Lavg=1.074, ra=0.0, reward=0.000
Completion 2: length=442, L/Lavg=0.707, ra=0.0, reward=0.000
Completion 3: length=724, L/Lavg=1.159, ra=0.0, reward=0.000
------------- 16-17-08-30-621440 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 821.75
Completion 0: length=614, L/Lavg=0.747, ra=0.0, reward=0.000
Completion 1: length=1240, L/Lavg=1.509, ra=0.0, reward=0.000
Completion 2: length=747, L/Lavg=0.909, ra=0.0, reward=0.000
Completion 3: length=686, L/Lavg=0.835, ra=0.0, reward=0.000
------------- 16-17-08-30-625128 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1014.75
Completion 0: length=977, L/Lavg=0.963, ra=0.0, reward=0.000
Completion 1: length=658, L/Lavg=0.648, ra=0.0, reward=0.000
Completion 2: length=1100, L/Lavg=1.084, ra=1.0, reward=-0.084
Completion 3: length=1324, L/Lavg=1.305, ra=0.0, reward=0.000
------------- 16-17-08-30-625927 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 827.25
Completion 0: length=700, L/Lavg=0.846, ra=1.0, reward=0.154
Completion 1: length=671, L/Lavg=0.811, ra=0.0, reward=0.000
Completion 2: length=689, L/Lavg=0.833, ra=0.0, reward=0.000
Completion 3: length=1249, L/Lavg=1.510, ra=0.0, reward=0.000
------------- 16-17-08-30-630096 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1090.25
Completion 0: length=1121, L/Lavg=1.028, ra=1.0, reward=-0.028
Completion 1: length=991, L/Lavg=0.909, ra=0.0, reward=0.000
Completion 2: length=659, L/Lavg=0.604, ra=0.0, reward=0.000
Completion 3: length=1590, L/Lavg=1.458, ra=0.0, reward=0.000
------------- 16-17-08-49-613181 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 1333.25
Completion 0: length=798, L/Lavg=0.599, ra=1.0, reward=0.000
Completion 1: length=1358, L/Lavg=1.019, ra=1.0, reward=0.000
Completion 2: length=1875, L/Lavg=1.406, ra=1.0, reward=0.000
Completion 3: length=1302, L/Lavg=0.977, ra=0.0, reward=-0.023
------------- 16-17-08-49-619161 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 772.75
Completion 0: length=688, L/Lavg=0.890, ra=1.0, reward=0.110
Completion 1: length=953, L/Lavg=1.233, ra=1.0, reward=-0.233
Completion 2: length=703, L/Lavg=0.910, ra=0.0, reward=0.000
Completion 3: length=747, L/Lavg=0.967, ra=0.0, reward=0.000
------------- 16-17-08-49-628762 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 978.75
Completion 0: length=1091, L/Lavg=1.115, ra=0.0, reward=0.000
Completion 1: length=1427, L/Lavg=1.458, ra=0.0, reward=0.000
Completion 2: length=619, L/Lavg=0.632, ra=0.0, reward=0.000
Completion 3: length=778, L/Lavg=0.795, ra=0.0, reward=0.000
------------- 16-17-08-49-684612 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 707.75
Completion 0: length=873, L/Lavg=1.233, ra=0.0, reward=0.000
Completion 1: length=614, L/Lavg=0.868, ra=1.0, reward=0.132
Completion 2: length=828, L/Lavg=1.170, ra=1.0, reward=-0.170
Completion 3: length=516, L/Lavg=0.729, ra=0.0, reward=0.000
------------- 16-17-09-09-846873 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 681.0
Completion 0: length=750, L/Lavg=1.101, ra=0.0, reward=0.000
Completion 1: length=535, L/Lavg=0.786, ra=0.0, reward=0.000
Completion 2: length=714, L/Lavg=1.048, ra=0.0, reward=0.000
Completion 3: length=725, L/Lavg=1.065, ra=0.0, reward=0.000
------------- 16-17-09-09-847972 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 780.25
Completion 0: length=488, L/Lavg=0.625, ra=0.0, reward=0.000
Completion 1: length=1308, L/Lavg=1.676, ra=1.0, reward=-0.676
Completion 2: length=711, L/Lavg=0.911, ra=1.0, reward=0.089
Completion 3: length=614, L/Lavg=0.787, ra=0.0, reward=0.000
------------- 16-17-09-09-861033 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1034.25
Completion 0: length=825, L/Lavg=0.798, ra=0.0, reward=0.000
Completion 1: length=592, L/Lavg=0.572, ra=0.0, reward=0.000
Completion 2: length=1297, L/Lavg=1.254, ra=0.0, reward=0.000
Completion 3: length=1423, L/Lavg=1.376, ra=0.0, reward=0.000
------------- 16-17-09-09-908189 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 701.75
Completion 0: length=602, L/Lavg=0.858, ra=0.0, reward=0.000
Completion 1: length=544, L/Lavg=0.775, ra=0.0, reward=0.000
Completion 2: length=982, L/Lavg=1.399, ra=1.0, reward=-0.399
Completion 3: length=679, L/Lavg=0.968, ra=1.0, reward=0.032
------------- 16-17-09-31-791699 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 612.75
Completion 0: length=598, L/Lavg=0.976, ra=0.0, reward=0.000
Completion 1: length=640, L/Lavg=1.044, ra=0.0, reward=0.000
Completion 2: length=457, L/Lavg=0.746, ra=0.0, reward=0.000
Completion 3: length=756, L/Lavg=1.234, ra=0.0, reward=0.000
------------- 16-17-09-31-799228 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 788.5
Completion 0: length=662, L/Lavg=0.840, ra=1.0, reward=0.160
Completion 1: length=609, L/Lavg=0.772, ra=0.0, reward=0.000
Completion 2: length=560, L/Lavg=0.710, ra=0.0, reward=0.000
Completion 3: length=1323, L/Lavg=1.678, ra=1.0, reward=-0.678
------------- 16-17-09-31-806627 New Length Reward Debug -------------
Group Accuracy: 1.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 949.0
Completion 0: length=779, L/Lavg=0.821, ra=1.0, reward=0.000
Completion 1: length=971, L/Lavg=1.023, ra=1.0, reward=0.000
Completion 2: length=681, L/Lavg=0.718, ra=1.0, reward=0.000
Completion 3: length=1365, L/Lavg=1.438, ra=1.0, reward=0.000
------------- 16-17-09-31-862962 New Length Reward Debug -------------
Group Accuracy: 1.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 723.0
Completion 0: length=520, L/Lavg=0.719, ra=1.0, reward=0.281
Completion 1: length=981, L/Lavg=1.357, ra=1.0, reward=-0.357
Completion 2: length=811, L/Lavg=1.122, ra=1.0, reward=-0.122
Completion 3: length=580, L/Lavg=0.802, ra=1.0, reward=0.198
------------- 16-17-09-56-259744 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 765.5
Completion 0: length=923, L/Lavg=1.206, ra=0.0, reward=0.000
Completion 1: length=690, L/Lavg=0.901, ra=0.0, reward=0.000
Completion 2: length=710, L/Lavg=0.927, ra=0.0, reward=0.000
Completion 3: length=739, L/Lavg=0.965, ra=0.0, reward=0.000
------------- 16-17-09-56-272103 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 794.0
Completion 0: length=528, L/Lavg=0.665, ra=1.0, reward=0.335
Completion 1: length=634, L/Lavg=0.798, ra=0.0, reward=0.000
Completion 2: length=622, L/Lavg=0.783, ra=1.0, reward=0.217
Completion 3: length=1392, L/Lavg=1.753, ra=0.0, reward=0.000
------------- 16-17-09-56-280474 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 698.75
Completion 0: length=719, L/Lavg=1.029, ra=0.0, reward=0.000
Completion 1: length=718, L/Lavg=1.028, ra=0.0, reward=0.000
Completion 2: length=552, L/Lavg=0.790, ra=0.0, reward=0.000
Completion 3: length=806, L/Lavg=1.153, ra=0.0, reward=0.000
------------- 16-17-09-56-327955 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 749.75
Completion 0: length=818, L/Lavg=1.091, ra=0.0, reward=0.000
Completion 1: length=605, L/Lavg=0.807, ra=1.0, reward=0.193
Completion 2: length=621, L/Lavg=0.828, ra=0.0, reward=0.000
Completion 3: length=955, L/Lavg=1.274, ra=1.0, reward=-0.274
------------- 16-17-10-18-107815 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 630.75
Completion 0: length=682, L/Lavg=1.081, ra=1.0, reward=0.000
Completion 1: length=446, L/Lavg=0.707, ra=0.0, reward=-0.293
Completion 2: length=737, L/Lavg=1.168, ra=0.0, reward=0.168
Completion 3: length=658, L/Lavg=1.043, ra=0.0, reward=0.043
------------- 16-17-10-18-109733 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 856.25
Completion 0: length=897, L/Lavg=1.048, ra=0.0, reward=0.000
Completion 1: length=509, L/Lavg=0.594, ra=0.0, reward=0.000
Completion 2: length=1102, L/Lavg=1.287, ra=1.0, reward=-0.287
Completion 3: length=917, L/Lavg=1.071, ra=0.0, reward=0.000
------------- 16-17-10-18-115496 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 950.0
Completion 0: length=984, L/Lavg=1.036, ra=0.0, reward=0.000
Completion 1: length=1258, L/Lavg=1.324, ra=0.0, reward=0.000
Completion 2: length=750, L/Lavg=0.789, ra=0.0, reward=0.000
Completion 3: length=808, L/Lavg=0.851, ra=0.0, reward=0.000
------------- 16-17-10-18-152713 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 986.25
Completion 0: length=978, L/Lavg=0.992, ra=0.0, reward=0.000
Completion 1: length=815, L/Lavg=0.826, ra=0.0, reward=0.000
Completion 2: length=1399, L/Lavg=1.419, ra=1.0, reward=-0.419
Completion 3: length=753, L/Lavg=0.763, ra=0.0, reward=0.000
------------- 16-17-10-37-245854 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 592.25
Completion 0: length=397, L/Lavg=0.670, ra=1.0, reward=0.000
Completion 1: length=629, L/Lavg=1.062, ra=0.0, reward=0.062
Completion 2: length=751, L/Lavg=1.268, ra=1.0, reward=0.000
Completion 3: length=592, L/Lavg=1.000, ra=1.0, reward=0.000
------------- 16-17-10-37-251895 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 962.0
Completion 0: length=965, L/Lavg=1.003, ra=0.0, reward=0.000
Completion 1: length=712, L/Lavg=0.740, ra=0.0, reward=0.000
Completion 2: length=1100, L/Lavg=1.143, ra=0.0, reward=0.000
Completion 3: length=1071, L/Lavg=1.113, ra=0.0, reward=0.000
------------- 16-17-10-37-255314 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 870.25
Completion 0: length=1347, L/Lavg=1.548, ra=0.0, reward=0.000
Completion 1: length=780, L/Lavg=0.896, ra=0.0, reward=0.000
Completion 2: length=903, L/Lavg=1.038, ra=0.0, reward=0.000
Completion 3: length=451, L/Lavg=0.518, ra=0.0, reward=0.000
------------- 16-17-10-37-255844 New Length Reward Debug -------------
Group Accuracy: 1.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 678.5
Completion 0: length=875, L/Lavg=1.290, ra=1.0, reward=-0.290
Completion 1: length=702, L/Lavg=1.035, ra=1.0, reward=-0.035
Completion 2: length=579, L/Lavg=0.853, ra=1.0, reward=0.147
Completion 3: length=558, L/Lavg=0.822, ra=1.0, reward=0.178
------------- 16-17-10-58-961001 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 689.5
Completion 0: length=886, L/Lavg=1.285, ra=0.0, reward=0.285
Completion 1: length=638, L/Lavg=0.925, ra=0.0, reward=-0.075
Completion 2: length=744, L/Lavg=1.079, ra=0.0, reward=0.079
Completion 3: length=490, L/Lavg=0.711, ra=0.0, reward=-0.289
------------- 16-17-10-58-968261 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1097.5
Completion 0: length=978, L/Lavg=0.891, ra=0.0, reward=0.000
Completion 1: length=897, L/Lavg=0.817, ra=0.0, reward=0.000
Completion 2: length=1773, L/Lavg=1.615, ra=0.0, reward=0.000
Completion 3: length=742, L/Lavg=0.676, ra=0.0, reward=0.000
------------- 16-17-10-58-972109 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 704.25
Completion 0: length=468, L/Lavg=0.665, ra=1.0, reward=0.000
Completion 1: length=955, L/Lavg=1.356, ra=1.0, reward=0.000
Completion 2: length=916, L/Lavg=1.301, ra=0.0, reward=0.301
Completion 3: length=478, L/Lavg=0.679, ra=1.0, reward=0.000
------------- 16-17-10-58-975217 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1493.25
Completion 0: length=794, L/Lavg=0.532, ra=1.0, reward=0.468
Completion 1: length=981, L/Lavg=0.657, ra=1.0, reward=0.343
Completion 2: length=1658, L/Lavg=1.110, ra=0.0, reward=0.000
Completion 3: length=2540, L/Lavg=1.701, ra=0.0, reward=0.000
------------- 16-17-11-19-395446 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1281.25
Completion 0: length=1153, L/Lavg=0.900, ra=0.0, reward=-0.100
Completion 1: length=1232, L/Lavg=0.962, ra=0.0, reward=-0.038
Completion 2: length=1744, L/Lavg=1.361, ra=0.0, reward=0.361
Completion 3: length=996, L/Lavg=0.777, ra=0.0, reward=-0.223
------------- 16-17-11-19-398522 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 906.0
Completion 0: length=1064, L/Lavg=1.174, ra=1.0, reward=-0.174
Completion 1: length=989, L/Lavg=1.092, ra=1.0, reward=-0.092
Completion 2: length=860, L/Lavg=0.949, ra=0.0, reward=0.000
Completion 3: length=711, L/Lavg=0.785, ra=0.0, reward=0.000
------------- 16-17-11-19-400216 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 953.0
Completion 0: length=1336, L/Lavg=1.402, ra=1.0, reward=-0.402
Completion 1: length=909, L/Lavg=0.954, ra=0.0, reward=0.000
Completion 2: length=1193, L/Lavg=1.252, ra=0.0, reward=0.000
Completion 3: length=374, L/Lavg=0.392, ra=0.0, reward=0.000
------------- 16-17-11-19-402435 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1132.75
Completion 0: length=1538, L/Lavg=1.358, ra=0.0, reward=0.000
Completion 1: length=600, L/Lavg=0.530, ra=0.0, reward=0.000
Completion 2: length=1663, L/Lavg=1.468, ra=0.0, reward=0.000
Completion 3: length=730, L/Lavg=0.644, ra=0.0, reward=0.000
------------- 16-17-11-38-062022 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 903.75
Completion 0: length=276, L/Lavg=0.305, ra=1.0, reward=0.695
Completion 1: length=750, L/Lavg=0.830, ra=0.0, reward=0.000
Completion 2: length=1557, L/Lavg=1.723, ra=0.0, reward=0.000
Completion 3: length=1032, L/Lavg=1.142, ra=1.0, reward=-0.142
------------- 16-17-11-38-064886 New Length Reward Debug -------------
Group Accuracy: 1.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 890.0
Completion 0: length=934, L/Lavg=1.049, ra=1.0, reward=0.000
Completion 1: length=1099, L/Lavg=1.235, ra=1.0, reward=0.000
Completion 2: length=709, L/Lavg=0.797, ra=1.0, reward=0.000
Completion 3: length=818, L/Lavg=0.919, ra=1.0, reward=0.000
------------- 16-17-11-38-067502 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 903.5
Completion 0: length=350, L/Lavg=0.387, ra=0.0, reward=0.000
Completion 1: length=975, L/Lavg=1.079, ra=0.0, reward=0.000
Completion 2: length=660, L/Lavg=0.730, ra=0.0, reward=0.000
Completion 3: length=1629, L/Lavg=1.803, ra=0.0, reward=0.000
------------- 16-17-11-38-080588 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1058.75
Completion 0: length=1042, L/Lavg=0.984, ra=0.0, reward=0.000
Completion 1: length=1188, L/Lavg=1.122, ra=0.0, reward=0.000
Completion 2: length=853, L/Lavg=0.806, ra=0.0, reward=0.000
Completion 3: length=1152, L/Lavg=1.088, ra=0.0, reward=0.000
------------- 16-17-11-59-176719 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 985.0
Completion 0: length=1435, L/Lavg=1.457, ra=0.0, reward=0.000
Completion 1: length=545, L/Lavg=0.553, ra=0.0, reward=0.000
Completion 2: length=840, L/Lavg=0.853, ra=0.0, reward=0.000
Completion 3: length=1120, L/Lavg=1.137, ra=0.0, reward=0.000
------------- 16-17-11-59-177545 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 804.5
Completion 0: length=722, L/Lavg=0.897, ra=0.0, reward=-0.103
Completion 1: length=706, L/Lavg=0.878, ra=1.0, reward=0.000
Completion 2: length=712, L/Lavg=0.885, ra=0.0, reward=-0.115
Completion 3: length=1078, L/Lavg=1.340, ra=0.0, reward=0.340
------------- 16-17-11-59-178973 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 983.75
Completion 0: length=965, L/Lavg=0.981, ra=0.0, reward=-0.019
Completion 1: length=817, L/Lavg=0.830, ra=0.0, reward=-0.170
Completion 2: length=1011, L/Lavg=1.028, ra=0.0, reward=0.028
Completion 3: length=1142, L/Lavg=1.161, ra=1.0, reward=0.000
------------- 16-17-11-59-238664 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 959.25
Completion 0: length=765, L/Lavg=0.797, ra=1.0, reward=0.203
Completion 1: length=1223, L/Lavg=1.275, ra=0.0, reward=0.000
Completion 2: length=714, L/Lavg=0.744, ra=0.0, reward=0.000
Completion 3: length=1135, L/Lavg=1.183, ra=0.0, reward=0.000
------------- 16-17-12-17-939612 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 1022.25
Completion 0: length=999, L/Lavg=0.977, ra=1.0, reward=0.023
Completion 1: length=807, L/Lavg=0.789, ra=1.0, reward=0.211
Completion 2: length=1319, L/Lavg=1.290, ra=0.0, reward=0.000
Completion 3: length=964, L/Lavg=0.943, ra=0.0, reward=0.000
------------- 16-17-12-17-945803 New Length Reward Debug -------------
Group Accuracy: 1.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 757.75
Completion 0: length=656, L/Lavg=0.866, ra=1.0, reward=0.134
Completion 1: length=701, L/Lavg=0.925, ra=1.0, reward=0.075
Completion 2: length=991, L/Lavg=1.308, ra=1.0, reward=-0.308
Completion 3: length=683, L/Lavg=0.901, ra=1.0, reward=0.099
------------- 16-17-12-17-946711 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 584.5
Completion 0: length=600, L/Lavg=1.027, ra=1.0, reward=0.000
Completion 1: length=501, L/Lavg=0.857, ra=0.0, reward=-0.143
Completion 2: length=582, L/Lavg=0.996, ra=0.0, reward=-0.004
Completion 3: length=655, L/Lavg=1.121, ra=0.0, reward=0.121
------------- 16-17-12-18-003740 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 688.0
Completion 0: length=592, L/Lavg=0.860, ra=1.0, reward=0.000
Completion 1: length=849, L/Lavg=1.234, ra=0.0, reward=0.234
Completion 2: length=484, L/Lavg=0.703, ra=0.0, reward=-0.297
Completion 3: length=827, L/Lavg=1.202, ra=1.0, reward=0.000
------------- 16-17-14-08-493122 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 752.25
Completion 0: length=582, L/Lavg=0.774, ra=0.0, reward=-0.226
Completion 1: length=1264, L/Lavg=1.680, ra=0.0, reward=0.680
Completion 2: length=743, L/Lavg=0.988, ra=0.0, reward=-0.012
Completion 3: length=420, L/Lavg=0.558, ra=0.0, reward=-0.442
------------- 16-17-14-08-493125 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 811.25
Completion 0: length=1146, L/Lavg=1.413, ra=1.0, reward=0.000
Completion 1: length=796, L/Lavg=0.981, ra=0.0, reward=-0.019
Completion 2: length=574, L/Lavg=0.708, ra=0.0, reward=-0.292
Completion 3: length=729, L/Lavg=0.899, ra=1.0, reward=0.000
------------- 16-17-14-08-494257 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-17-14-08-494303 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=-0.117
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.091
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.056
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=-0.030
------------- 16-17-14-31-520689 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 624.75
Completion 0: length=662, L/Lavg=1.060, ra=0.0, reward=0.000
Completion 1: length=671, L/Lavg=1.074, ra=0.0, reward=0.000
Completion 2: length=442, L/Lavg=0.707, ra=0.0, reward=0.000
Completion 3: length=724, L/Lavg=1.159, ra=0.0, reward=0.000
------------- 16-17-14-31-576409 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 932.5
Completion 0: length=913, L/Lavg=0.979, ra=0.0, reward=0.000
Completion 1: length=1030, L/Lavg=1.105, ra=0.0, reward=0.000
Completion 2: length=811, L/Lavg=0.870, ra=0.0, reward=0.000
Completion 3: length=976, L/Lavg=1.047, ra=0.0, reward=0.000
------------- 16-17-14-31-579574 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 686.0
Completion 0: length=763, L/Lavg=1.112, ra=0.0, reward=0.000
Completion 1: length=729, L/Lavg=1.063, ra=0.0, reward=0.000
Completion 2: length=776, L/Lavg=1.131, ra=1.0, reward=-0.131
Completion 3: length=476, L/Lavg=0.694, ra=0.0, reward=0.000
------------- 16-17-14-31-638467 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 888.75
Completion 0: length=684, L/Lavg=0.770, ra=0.0, reward=0.000
Completion 1: length=828, L/Lavg=0.932, ra=1.0, reward=0.068
Completion 2: length=872, L/Lavg=0.981, ra=1.0, reward=0.019
Completion 3: length=1171, L/Lavg=1.318, ra=1.0, reward=-0.318
------------- 16-17-14-49-288313 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1225.5
Completion 0: length=1782, L/Lavg=1.454, ra=1.0, reward=-0.454
Completion 1: length=1033, L/Lavg=0.843, ra=0.0, reward=0.000
Completion 2: length=1289, L/Lavg=1.052, ra=1.0, reward=-0.052
Completion 3: length=798, L/Lavg=0.651, ra=0.0, reward=0.000
------------- 16-17-14-49-298487 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 773.25
Completion 0: length=1166, L/Lavg=1.508, ra=0.0, reward=0.000
Completion 1: length=612, L/Lavg=0.791, ra=0.0, reward=0.000
Completion 2: length=862, L/Lavg=1.115, ra=0.0, reward=0.000
Completion 3: length=453, L/Lavg=0.586, ra=0.0, reward=0.000
------------- 16-17-14-49-307835 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 640.75
Completion 0: length=814, L/Lavg=1.270, ra=0.0, reward=0.000
Completion 1: length=662, L/Lavg=1.033, ra=0.0, reward=0.000
Completion 2: length=484, L/Lavg=0.755, ra=0.0, reward=0.000
Completion 3: length=603, L/Lavg=0.941, ra=0.0, reward=0.000
------------- 16-17-14-49-322229 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1215.25
Completion 0: length=909, L/Lavg=0.748, ra=0.0, reward=0.000
Completion 1: length=1511, L/Lavg=1.243, ra=0.0, reward=0.000
Completion 2: length=1100, L/Lavg=0.905, ra=1.0, reward=0.095
Completion 3: length=1341, L/Lavg=1.103, ra=0.0, reward=0.000
------------- 16-17-15-08-042163 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 687.25
Completion 0: length=649, L/Lavg=0.944, ra=0.0, reward=-0.056
Completion 1: length=796, L/Lavg=1.158, ra=0.0, reward=0.158
Completion 2: length=895, L/Lavg=1.302, ra=0.0, reward=0.302
Completion 3: length=409, L/Lavg=0.595, ra=1.0, reward=0.000
------------- 16-17-15-08-047190 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 1263.5
Completion 0: length=1157, L/Lavg=0.916, ra=1.0, reward=0.084
Completion 1: length=1824, L/Lavg=1.444, ra=1.0, reward=-0.444
Completion 2: length=632, L/Lavg=0.500, ra=0.0, reward=0.000
Completion 3: length=1441, L/Lavg=1.140, ra=0.0, reward=0.000
------------- 16-17-15-08-055512 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 902.75
Completion 0: length=794, L/Lavg=0.880, ra=1.0, reward=0.120
Completion 1: length=1224, L/Lavg=1.356, ra=0.0, reward=0.000
Completion 2: length=999, L/Lavg=1.107, ra=1.0, reward=-0.107
Completion 3: length=594, L/Lavg=0.658, ra=0.0, reward=0.000
------------- 16-17-15-08-061075 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 863.5
Completion 0: length=1235, L/Lavg=1.430, ra=0.0, reward=0.430
Completion 1: length=785, L/Lavg=0.909, ra=0.0, reward=-0.091
Completion 2: length=1042, L/Lavg=1.207, ra=0.0, reward=0.207
Completion 3: length=392, L/Lavg=0.454, ra=1.0, reward=0.000
------------- 16-17-15-26-964539 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1089.5
Completion 0: length=1015, L/Lavg=0.932, ra=0.0, reward=0.000
Completion 1: length=1754, L/Lavg=1.610, ra=0.0, reward=0.000
Completion 2: length=746, L/Lavg=0.685, ra=0.0, reward=0.000
Completion 3: length=843, L/Lavg=0.774, ra=0.0, reward=0.000
------------- 16-17-15-26-966436 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 816.0
Completion 0: length=786, L/Lavg=0.963, ra=0.0, reward=-0.037
Completion 1: length=707, L/Lavg=0.866, ra=1.0, reward=0.000
Completion 2: length=1015, L/Lavg=1.244, ra=0.0, reward=0.244
Completion 3: length=756, L/Lavg=0.926, ra=0.0, reward=-0.074
------------- 16-17-15-26-972334 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 595.75
Completion 0: length=417, L/Lavg=0.700, ra=0.0, reward=0.000
Completion 1: length=746, L/Lavg=1.252, ra=0.0, reward=0.000
Completion 2: length=677, L/Lavg=1.136, ra=0.0, reward=0.000
Completion 3: length=543, L/Lavg=0.911, ra=0.0, reward=0.000
------------- 16-17-15-26-979663 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 835.75
Completion 0: length=909, L/Lavg=1.088, ra=0.0, reward=0.000
Completion 1: length=820, L/Lavg=0.981, ra=0.0, reward=0.000
Completion 2: length=704, L/Lavg=0.842, ra=0.0, reward=0.000
Completion 3: length=910, L/Lavg=1.089, ra=1.0, reward=-0.089
------------- 16-17-15-46-142003 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 921.5
Completion 0: length=966, L/Lavg=1.048, ra=1.0, reward=-0.048
Completion 1: length=1162, L/Lavg=1.261, ra=0.0, reward=0.000
Completion 2: length=526, L/Lavg=0.571, ra=1.0, reward=0.429
Completion 3: length=1032, L/Lavg=1.120, ra=0.0, reward=0.000
------------- 16-17-15-46-145367 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 792.5
Completion 0: length=933, L/Lavg=1.177, ra=1.0, reward=0.000
Completion 1: length=669, L/Lavg=0.844, ra=0.0, reward=-0.156
Completion 2: length=595, L/Lavg=0.751, ra=1.0, reward=0.000
Completion 3: length=973, L/Lavg=1.228, ra=1.0, reward=0.000
------------- 16-17-15-46-152653 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 686.25
Completion 0: length=540, L/Lavg=0.787, ra=0.0, reward=0.000
Completion 1: length=722, L/Lavg=1.052, ra=0.0, reward=0.000
Completion 2: length=475, L/Lavg=0.692, ra=0.0, reward=0.000
Completion 3: length=1008, L/Lavg=1.469, ra=0.0, reward=0.000
------------- 16-17-15-46-160575 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 663.75
Completion 0: length=757, L/Lavg=1.140, ra=0.0, reward=0.140
Completion 1: length=749, L/Lavg=1.128, ra=0.0, reward=0.128
Completion 2: length=661, L/Lavg=0.996, ra=1.0, reward=0.000
Completion 3: length=488, L/Lavg=0.735, ra=1.0, reward=0.000
------------- 16-17-16-04-050115 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 587.75
Completion 0: length=508, L/Lavg=0.864, ra=0.0, reward=-0.136
Completion 1: length=543, L/Lavg=0.924, ra=0.0, reward=-0.076
Completion 2: length=622, L/Lavg=1.058, ra=0.0, reward=0.058
Completion 3: length=678, L/Lavg=1.154, ra=0.0, reward=0.154
------------- 16-17-16-04-050137 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 701.75
Completion 0: length=629, L/Lavg=0.896, ra=1.0, reward=0.000
Completion 1: length=912, L/Lavg=1.300, ra=0.0, reward=0.300
Completion 2: length=448, L/Lavg=0.638, ra=1.0, reward=0.000
Completion 3: length=818, L/Lavg=1.166, ra=1.0, reward=0.000
------------- 16-17-16-04-055958 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 951.25
Completion 0: length=829, L/Lavg=0.871, ra=1.0, reward=0.129
Completion 1: length=535, L/Lavg=0.562, ra=0.0, reward=0.000
Completion 2: length=819, L/Lavg=0.861, ra=0.0, reward=0.000
Completion 3: length=1622, L/Lavg=1.705, ra=0.0, reward=0.000
------------- 16-17-16-04-060729 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 743.75
Completion 0: length=820, L/Lavg=1.103, ra=0.0, reward=0.103
Completion 1: length=924, L/Lavg=1.242, ra=1.0, reward=0.000
Completion 2: length=357, L/Lavg=0.480, ra=1.0, reward=0.000
Completion 3: length=874, L/Lavg=1.175, ra=0.0, reward=0.175
------------- 16-17-16-24-109805 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1103.25
Completion 0: length=1445, L/Lavg=1.310, ra=0.0, reward=0.000
Completion 1: length=661, L/Lavg=0.599, ra=0.0, reward=0.000
Completion 2: length=1165, L/Lavg=1.056, ra=0.0, reward=0.000
Completion 3: length=1142, L/Lavg=1.035, ra=0.0, reward=0.000
------------- 16-17-16-24-120976 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1024.75
Completion 0: length=924, L/Lavg=0.902, ra=0.0, reward=0.000
Completion 1: length=866, L/Lavg=0.845, ra=0.0, reward=0.000
Completion 2: length=1100, L/Lavg=1.073, ra=0.0, reward=0.000
Completion 3: length=1209, L/Lavg=1.180, ra=0.0, reward=0.000
------------- 16-17-16-24-122696 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 714.75
Completion 0: length=873, L/Lavg=1.221, ra=0.0, reward=0.000
Completion 1: length=593, L/Lavg=0.830, ra=0.0, reward=0.000
Completion 2: length=596, L/Lavg=0.834, ra=1.0, reward=0.166
Completion 3: length=797, L/Lavg=1.115, ra=0.0, reward=0.000
------------- 16-17-16-24-123720 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1121.5
Completion 0: length=1242, L/Lavg=1.107, ra=0.0, reward=0.000
Completion 1: length=1006, L/Lavg=0.897, ra=1.0, reward=0.103
Completion 2: length=969, L/Lavg=0.864, ra=1.0, reward=0.136
Completion 3: length=1269, L/Lavg=1.132, ra=0.0, reward=0.000
------------- 16-17-16-40-991575 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 756.25
Completion 0: length=602, L/Lavg=0.796, ra=1.0, reward=0.204
Completion 1: length=601, L/Lavg=0.795, ra=0.0, reward=0.000
Completion 2: length=1060, L/Lavg=1.402, ra=0.0, reward=0.000
Completion 3: length=762, L/Lavg=1.008, ra=0.0, reward=0.000
------------- 16-17-16-40-995309 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 786.0
Completion 0: length=804, L/Lavg=1.023, ra=0.0, reward=0.000
Completion 1: length=659, L/Lavg=0.838, ra=1.0, reward=0.162
Completion 2: length=857, L/Lavg=1.090, ra=1.0, reward=-0.090
Completion 3: length=824, L/Lavg=1.048, ra=1.0, reward=-0.048
------------- 16-17-16-40-997531 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 810.25
Completion 0: length=1162, L/Lavg=1.434, ra=0.0, reward=0.000
Completion 1: length=797, L/Lavg=0.984, ra=0.0, reward=0.000
Completion 2: length=696, L/Lavg=0.859, ra=0.0, reward=0.000
Completion 3: length=586, L/Lavg=0.723, ra=0.0, reward=0.000
------------- 16-17-16-41-013190 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 915.25
Completion 0: length=852, L/Lavg=0.931, ra=0.0, reward=0.000
Completion 1: length=1344, L/Lavg=1.468, ra=0.0, reward=0.000
Completion 2: length=723, L/Lavg=0.790, ra=0.0, reward=0.000
Completion 3: length=742, L/Lavg=0.811, ra=0.0, reward=0.000
------------- 16-17-16-58-855969 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 843.25
Completion 0: length=990, L/Lavg=1.174, ra=0.0, reward=0.000
Completion 1: length=877, L/Lavg=1.040, ra=0.0, reward=0.000
Completion 2: length=846, L/Lavg=1.003, ra=0.0, reward=0.000
Completion 3: length=660, L/Lavg=0.783, ra=0.0, reward=0.000
------------- 16-17-16-58-857550 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 709.0
Completion 0: length=800, L/Lavg=1.128, ra=0.0, reward=0.000
Completion 1: length=447, L/Lavg=0.630, ra=0.0, reward=0.000
Completion 2: length=762, L/Lavg=1.075, ra=0.0, reward=0.000
Completion 3: length=827, L/Lavg=1.166, ra=0.0, reward=0.000
------------- 16-17-16-58-860239 New Length Reward Debug -------------
Group Accuracy: 1.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 836.25
Completion 0: length=881, L/Lavg=1.054, ra=1.0, reward=0.000
Completion 1: length=746, L/Lavg=0.892, ra=1.0, reward=0.000
Completion 2: length=674, L/Lavg=0.806, ra=1.0, reward=0.000
Completion 3: length=1044, L/Lavg=1.248, ra=1.0, reward=0.000
------------- 16-17-16-58-862578 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 830.5
Completion 0: length=745, L/Lavg=0.897, ra=0.0, reward=0.000
Completion 1: length=878, L/Lavg=1.057, ra=0.0, reward=0.000
Completion 2: length=1140, L/Lavg=1.373, ra=0.0, reward=0.000
Completion 3: length=559, L/Lavg=0.673, ra=0.0, reward=0.000
------------- 16-17-17-20-839240 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 984.0
Completion 0: length=1071, L/Lavg=1.088, ra=0.0, reward=0.000
Completion 1: length=941, L/Lavg=0.956, ra=0.0, reward=0.000
Completion 2: length=1235, L/Lavg=1.255, ra=1.0, reward=-0.255
Completion 3: length=689, L/Lavg=0.700, ra=0.0, reward=0.000
------------- 16-17-17-20-843300 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1110.25
Completion 0: length=1548, L/Lavg=1.394, ra=0.0, reward=0.000
Completion 1: length=1055, L/Lavg=0.950, ra=0.0, reward=0.000
Completion 2: length=1228, L/Lavg=1.106, ra=0.0, reward=0.000
Completion 3: length=610, L/Lavg=0.549, ra=0.0, reward=0.000
------------- 16-17-17-20-845691 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1032.25
Completion 0: length=964, L/Lavg=0.934, ra=0.0, reward=0.000
Completion 1: length=755, L/Lavg=0.731, ra=0.0, reward=0.000
Completion 2: length=717, L/Lavg=0.695, ra=0.0, reward=0.000
Completion 3: length=1693, L/Lavg=1.640, ra=0.0, reward=0.000
------------- 16-17-17-20-850449 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1002.25
Completion 0: length=1219, L/Lavg=1.216, ra=1.0, reward=0.000
Completion 1: length=895, L/Lavg=0.893, ra=1.0, reward=0.000
Completion 2: length=726, L/Lavg=0.724, ra=1.0, reward=0.000
Completion 3: length=1169, L/Lavg=1.166, ra=0.0, reward=0.166
------------- 16-17-17-37-594459 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 974.25
Completion 0: length=643, L/Lavg=0.660, ra=0.0, reward=0.000
Completion 1: length=1363, L/Lavg=1.399, ra=0.0, reward=0.000
Completion 2: length=981, L/Lavg=1.007, ra=0.0, reward=0.000
Completion 3: length=910, L/Lavg=0.934, ra=0.0, reward=0.000
------------- 16-17-17-37-609847 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 733.75
Completion 0: length=658, L/Lavg=0.897, ra=0.0, reward=0.000
Completion 1: length=1035, L/Lavg=1.411, ra=1.0, reward=-0.411
Completion 2: length=531, L/Lavg=0.724, ra=1.0, reward=0.276
Completion 3: length=711, L/Lavg=0.969, ra=1.0, reward=0.031
------------- 16-17-17-37-610088 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1004.0
Completion 0: length=1097, L/Lavg=1.093, ra=0.0, reward=0.093
Completion 1: length=851, L/Lavg=0.848, ra=0.0, reward=-0.152
Completion 2: length=1133, L/Lavg=1.128, ra=0.0, reward=0.128
Completion 3: length=935, L/Lavg=0.931, ra=0.0, reward=-0.069
------------- 16-17-17-37-617030 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1007.5
Completion 0: length=1374, L/Lavg=1.364, ra=0.0, reward=0.000
Completion 1: length=702, L/Lavg=0.697, ra=0.0, reward=0.000
Completion 2: length=666, L/Lavg=0.661, ra=0.0, reward=0.000
Completion 3: length=1288, L/Lavg=1.278, ra=0.0, reward=0.000
------------- 16-17-17-58-121775 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 871.5
Completion 0: length=769, L/Lavg=0.882, ra=0.0, reward=0.000
Completion 1: length=877, L/Lavg=1.006, ra=0.0, reward=0.000
Completion 2: length=1174, L/Lavg=1.347, ra=0.0, reward=0.000
Completion 3: length=666, L/Lavg=0.764, ra=0.0, reward=0.000
------------- 16-17-17-58-131746 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 630.25
Completion 0: length=609, L/Lavg=0.966, ra=0.0, reward=0.000
Completion 1: length=867, L/Lavg=1.376, ra=0.0, reward=0.000
Completion 2: length=583, L/Lavg=0.925, ra=0.0, reward=0.000
Completion 3: length=462, L/Lavg=0.733, ra=0.0, reward=0.000
------------- 16-17-17-58-132138 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1540.25
Completion 0: length=1964, L/Lavg=1.275, ra=0.0, reward=0.000
Completion 1: length=849, L/Lavg=0.551, ra=0.0, reward=0.000
Completion 2: length=1347, L/Lavg=0.875, ra=1.0, reward=0.125
Completion 3: length=2001, L/Lavg=1.299, ra=0.0, reward=0.000
------------- 16-17-17-58-138026 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 674.25
Completion 0: length=598, L/Lavg=0.887, ra=0.0, reward=0.000
Completion 1: length=636, L/Lavg=0.943, ra=0.0, reward=0.000
Completion 2: length=642, L/Lavg=0.952, ra=1.0, reward=0.048
Completion 3: length=821, L/Lavg=1.218, ra=0.0, reward=0.000
